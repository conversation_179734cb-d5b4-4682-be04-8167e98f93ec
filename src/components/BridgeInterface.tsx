"use client";
import { useWallet } from "@solana/wallet-adapter-react";
import { useWalletModal } from "@solana/wallet-adapter-react-ui";
import { useAccount, useWriteContract, useWaitForTransactionReceipt, useConnect, useDisconnect } from "wagmi";
import { oft } from "@layerzerolabs/oft-v2-solana-sdk";
import { useState, useEffect, useCallback, useMemo } from "react";
import { EndpointId } from "@layerzerolabs/lz-definitions";
import { publicKey, transactionBuilder } from "@metaplex-foundation/umi";
import { createUmi } from "@metaplex-foundation/umi-bundle-defaults";
import { addressToBytes32 } from "@layerzerolabs/lz-v2-utilities";
import { walletAdapterIdentity } from "@metaplex-foundation/umi-signer-wallet-adapters";
import { Connection, PublicKey, TransactionInstruction, AddressLookupTableAccount } from "@solana/web3.js";
import { getAssociatedTokenAddress, getAccount, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { fromWeb3JsPublicKey, toWeb3JsTransaction, toWeb3JsPublicKey, toWeb3JsInstruction } from '@metaplex-foundation/umi-web3js-adapters';
import { findAssociatedTokenPda, createSplAssociatedTokenProgram, setComputeUnitLimit, setComputeUnitPrice, fetchAddressLookupTable } from '@metaplex-foundation/mpl-toolbox';
import type { AddressLookupTableInput, Instruction, TransactionBuilder, PublicKey as UmiPublicKey } from '@metaplex-foundation/umi';
import bs58 from 'bs58';
import { useReadContract } from 'wagmi';
import { erc20Abi, parseUnits } from 'viem';


const SOLANA_RPC_URL = process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.mainnet-beta.solana.com";
const SOLANA_OFT_MINT_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_OFT_MINT_ADDRESS;
const SOLANA_ESCROW_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_ESCROW_ADDRESS;
const SOLANA_PROGRAM_ADDRESS = process.env.NEXT_PUBLIC_SOLANA_PROGRAM_ADDRESS;
const ETHEREUM_OFT_ADDRESS = process.env.NEXT_PUBLIC_ETHEREUM_OFT_ADDRESS;
const DEFAULT_AMOUNT = parseFloat(process.env.NEXT_PUBLIC_DEFAULT_BRIDGE_AMOUNT || "0.1");
const ETHEREUM_MAINNET_EID = EndpointId.ETHEREUM_V2_MAINNET;
const SOLANA_MAINNET_EID = EndpointId.SOLANA_V2_MAINNET;

const SOLANA_TOKEN_DECIMALS = 6;
const ETHEREUM_TOKEN_DECIMALS = 18;

// Address Lookup Table mapping for different endpoint IDs
const LOOKUP_TABLE_ADDRESS: Record<number, string> = {
  [EndpointId.SOLANA_V2_MAINNET]: "AokBxha6VMLLgf97B5VYHEtqztamWmYERBmmFvjuTzJB", // Replace with actual LUT address
  [EndpointId.SOLANA_V2_TESTNET]: "AokBxha6VMLLgf97B5VYHEtqztamWmYERBmmFvjuTzJB", // Replace with actual LUT address
};

// Helper function to format endpoint ID for error messages
const formatEid = (eid: EndpointId): string => {
  switch (eid) {
    case EndpointId.SOLANA_V2_MAINNET:
      return "Solana Mainnet";
    case EndpointId.SOLANA_V2_TESTNET:
      return "Solana Testnet";
    case EndpointId.ETHEREUM_V2_MAINNET:
      return "Ethereum Mainnet";
    default:
      return `EID ${eid}`;
  }
};

// Transaction types for compute unit estimation
enum TransactionType {
  SendOFT = 'SendOFT',
  Quote = 'Quote',
}

// Hardcoded compute unit estimates for different transaction types
const TransactionCuEstimates: Record<TransactionType, number> = {
  [TransactionType.SendOFT]: 250_000, // Increased from 400k to 600k for LayerZero OFT transactions
  [TransactionType.Quote]: 200_000,
};

const getLayerZeroScanLink = (txHash: string, isTestnet: boolean = false): string => {
  const baseUrl = isTestnet ? 'https://testnet.layerzeroscan.com' : 'https://layerzeroscan.com';
  return `${baseUrl}/tx/${txHash}`;
};

const fetchGasPrice = async (): Promise<{ maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null> => {
  try {
    const response = await fetch('https://api.blocknative.com/gasprices/blockprices?chainid=1');
    const data = await response.json();

    if (data.blockPrices && data.blockPrices.length > 0) {
      const estimatedPrices = data.blockPrices[0].estimatedPrices;
      const price90 = estimatedPrices.find((p: { confidence: number; maxFeePerGas: number; maxPriorityFeePerGas: number }) => p.confidence === 90);

      if (price90) {
        const maxFeePerGas = BigInt(Math.ceil(price90.maxFeePerGas * 1e9));
        const maxPriorityFeePerGas = BigInt(Math.ceil(price90.maxPriorityFeePerGas * 1e9));

        return { maxFeePerGas, maxPriorityFeePerGas };
      }
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch gas price:', error);
    return null;
  }
};

const ETHEREUM_GAS_LIMIT = BigInt(250000);

const isValidEthereumAddress = (address: string): boolean => {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
};

const isValidSolanaAddress = (address: string): boolean => {
  try {
    const decoded = bs58.decode(address);
    return decoded.length === 32;
  } catch {
    return false;
  }
};

// Address Lookup Table utility function
const getAddressLookupTable = async (connection: Connection, umi: ReturnType<typeof createUmi>, fromEid: EndpointId) => {
  // Lookup Table Address and Priority Fee Calculation
  const lookupTableAddress = LOOKUP_TABLE_ADDRESS[fromEid];
  if (!lookupTableAddress) {
    throw new Error(`No lookup table found for ${formatEid(fromEid)}`);
  }

  const lookupTablePublicKey = publicKey(lookupTableAddress);
  const addressLookupTableInput: AddressLookupTableInput = await fetchAddressLookupTable(umi, lookupTablePublicKey);
  if (!addressLookupTableInput) {
    throw new Error(`No address lookup table found for ${lookupTableAddress}`);
  }

  const { value: lookupTableAccount } = await connection.getAddressLookupTable(toWeb3JsPublicKey(lookupTablePublicKey));
  if (!lookupTableAccount) {
    throw new Error(`No address lookup table account found for ${lookupTableAddress}`);
  }

  return {
    lookupTableAddress: lookupTablePublicKey,
    addressLookupTableInput,
    lookupTableAccount,
  };
};

// Helper function to get prioritization fees
const getPrioritizationFees = async (connection: Connection) => {
  try {
    const recentPrioritizationFees = await connection.getRecentPrioritizationFees();
    const fees = recentPrioritizationFees.map(fee => fee.prioritizationFee).filter(fee => fee > 0);
    const averageFeeExcludingZeros = fees.length > 0 ? fees.reduce((a, b) => a + b, 0) / fees.length : 1000;
    return { averageFeeExcludingZeros };
  } catch (error) {
    console.warn('Failed to get prioritization fees, using default:', error);
    return { averageFeeExcludingZeros: 1000 };
  }
};

// Helper function to simulate transaction and get compute units
const getSimulationComputeUnits = async (
  connection: Connection,
  instructions: TransactionInstruction[],
  payer: PublicKey,
  lookupTableAccounts: AddressLookupTableAccount[]
) => {
  try {
    const testTransaction = new (await import('@solana/web3.js')).VersionedTransaction(
      new (await import('@solana/web3.js')).TransactionMessage({
        payerKey: payer,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
        instructions,
      }).compileToV0Message(lookupTableAccounts)
    );

    const simulation = await connection.simulateTransaction(testTransaction);
    return simulation.value.unitsConsumed || TransactionCuEstimates[TransactionType.SendOFT];
  } catch (error) {
    console.warn('Failed to simulate transaction, using default compute units:', error);
    return TransactionCuEstimates[TransactionType.SendOFT];
  }
};

// Get compute unit price and limit
const getComputeUnitPriceAndLimit = async (
  connection: Connection,
  ixs: Instruction[],
  wallet: { publicKey: UmiPublicKey }, // Simplified wallet interface
  lookupTableAccount: AddressLookupTableAccount,
  transactionType: TransactionType
) => {
  const { averageFeeExcludingZeros } = await getPrioritizationFees(connection);
  const priorityFee = Math.round(averageFeeExcludingZeros);
  const computeUnitPrice = BigInt(priorityFee);

  let computeUnits;

  try {
    const simulatedUnits = await getSimulationComputeUnits(
      connection,
      ixs.map((ix) => toWeb3JsInstruction(ix)),
      toWeb3JsPublicKey(wallet.publicKey),
      [lookupTableAccount]
    );
    // Use the higher of simulated units or our hardcoded estimate for safety
    computeUnits = Math.max(simulatedUnits, TransactionCuEstimates[transactionType]);
  } catch (e) {
    console.error(`Error retrieving simulations compute units from RPC:`, e);
    console.log(
      `Falling back to hardcoded estimate for ${transactionType}: ${TransactionCuEstimates[transactionType]} CUs`
    );
    computeUnits = TransactionCuEstimates[transactionType];
  }

  if (!computeUnits) {
    throw new Error('Unable to compute units');
  }

  // Ensure we have a minimum of 400k compute units for LayerZero transactions
  const minComputeUnits = 400_000;
  computeUnits = Math.max(computeUnits, minComputeUnits);

  return {
    computeUnitPrice,
    computeUnits,
  };
};

// Add compute unit instructions with address lookup table
const addComputeUnitInstructions = async (
  connection: Connection,
  umi: ReturnType<typeof createUmi>,
  eid: EndpointId,
  txBuilder: TransactionBuilder,
  umiWalletSigner: { publicKey: UmiPublicKey }, // Simplified wallet interface
  computeUnitPriceScaleFactor: number,
  transactionType: TransactionType
) => {
  const computeUnitLimitScaleFactor = 1.3; // Increased to 1.3 for more conservative estimates
  const { addressLookupTableInput, lookupTableAccount } = await getAddressLookupTable(connection, umi, eid);
  const { computeUnitPrice, computeUnits } = await getComputeUnitPriceAndLimit(
    connection,
    txBuilder.getInstructions(),
    umiWalletSigner,
    lookupTableAccount,
    transactionType
  );

  // Since transaction builders are immutable, we must be careful to always assign the result
  const newTxBuilder = transactionBuilder()
    .add(
      setComputeUnitPrice(umi, {
        microLamports: computeUnitPrice * BigInt(Math.floor(computeUnitPriceScaleFactor)),
      })
    )
    .add(setComputeUnitLimit(umi, { units: computeUnits * computeUnitLimitScaleFactor }))
    .setAddressLookupTables([addressLookupTableInput])
    .add(txBuilder);

  return newTxBuilder;
};

const oftAbi = [
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      { "internalType": "bool", "name": "_payInLzToken", "type": "bool" }
    ],
    "name": "quoteSend",
    "outputs": [
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "msgFee",
        "type": "tuple"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {
        "components": [
          { "internalType": "uint32", "name": "dstEid", "type": "uint32" },
          { "internalType": "bytes32", "name": "to", "type": "bytes32" },
          { "internalType": "uint256", "name": "amountLD", "type": "uint256" },
          { "internalType": "uint256", "name": "minAmountLD", "type": "uint256" },
          { "internalType": "bytes", "name": "extraOptions", "type": "bytes" },
          { "internalType": "bytes", "name": "composeMsg", "type": "bytes" },
          { "internalType": "bytes", "name": "oftCmd", "type": "bytes" }
        ],
        "internalType": "struct SendParam",
        "name": "_sendParam",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
          { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
        ],
        "internalType": "struct MessagingFee",
        "name": "_fee",
        "type": "tuple"
      },
      { "internalType": "address", "name": "_refundAddress", "type": "address" }
    ],
    "name": "send",
    "outputs": [
      {
        "components": [
          { "internalType": "bytes32", "name": "guid", "type": "bytes32" },
          { "internalType": "uint64", "name": "nonce", "type": "uint64" },
          {
            "components": [
              { "internalType": "uint256", "name": "nativeFee", "type": "uint256" },
              { "internalType": "uint256", "name": "lzTokenFee", "type": "uint256" }
            ],
            "internalType": "struct MessagingFee",
            "name": "fee",
            "type": "tuple"
          }
        ],
        "internalType": "struct MessagingReceipt",
        "name": "msgReceipt",
        "type": "tuple"
      },
      {
        "components": [
          { "internalType": "uint256", "name": "amountSentLD", "type": "uint256" },
          { "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256" }
        ],
        "internalType": "struct OFTReceipt",
        "name": "oftReceipt",
        "type": "tuple"
      }
    ],
    "stateMutability": "payable",
    "type": "function"
  }
] as const;

interface BridgeState {
  isLoading: boolean;
  error: string | null;
  txHash: string | null;
  nativeFee: bigint | null;
  receiveAmount: string | null;
  solanaBalance: string | null;
  ethereumBalance: string | null;
  layerZeroScanLink: string | null;
  gasPrice: { maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } | null;
  customEthAddress: string;
  customSolanaAddress: string;
  isQuoting: boolean;
  isBridging: boolean;
  isPollingDestination: boolean;
}

interface Transaction {
  hash: string;
  timestamp: number;
  fromChain: 'solana' | 'ethereum';
  toChain: 'solana' | 'ethereum';
  amount: string;
  status: 'pending' | 'confirmed' | 'failed';
  layerZeroScanLink?: string;
}

export default function BridgeInterface() {
  const solanaWallet = useWallet();
  const { setVisible: setSolanaWalletModalVisible } = useWalletModal();
  const { address: ethAddress, isConnected: isEthConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { connect, connectors, isPending: isConnectPending } = useConnect();

  const [isClient, setIsClient] = useState(false);
  const [isEthWalletModalOpen, setIsEthWalletModalOpen] = useState(false);
  const [amount, setAmount] = useState(DEFAULT_AMOUNT.toString());
  const [direction, setDirection] = useState<'sol-to-eth' | 'eth-to-sol'>('sol-to-eth');
  const [bridgeState, setBridgeState] = useState<BridgeState>({
    isLoading: false,
    error: null,
    txHash: null,
    nativeFee: null,
    receiveAmount: null,
    solanaBalance: null,
    ethereumBalance: null,
    layerZeroScanLink: null,
    gasPrice: null,
    customEthAddress: '',
    customSolanaAddress: '',
    isQuoting: false,
    isBridging: false,
    isPollingDestination: false,
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isHistoryVisible, setIsHistoryVisible] = useState(false);

  const umi = useMemo(() => {
    const umiInstance = createUmi(SOLANA_RPC_URL);
    if (solanaWallet.wallet) {
      umiInstance.use(walletAdapterIdentity(solanaWallet));
    }
    umiInstance.programs.add(createSplAssociatedTokenProgram());
    return umiInstance;
  }, [solanaWallet]);

  const { data: ethereumBalanceRaw } = useReadContract({
    address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
    abi: erc20Abi,
    functionName: 'balanceOf',
    args: [ethAddress as `0x${string}`],
    query: {
      enabled: !!ethAddress && !!ETHEREUM_OFT_ADDRESS && isEthConnected,
    },
  });

  const { writeContract: writeOftContract, data: ethTxHash, isPending: isEthTxPending, error: ethTxError } = useWriteContract();
  const { isSuccess: isEthTxSuccess } = useWaitForTransactionReceipt({
    hash: ethTxHash,
  });

  const fetchSolanaBalance = useCallback(async (customAddress?: string) => {
    const addressToUse = customAddress || solanaWallet.publicKey?.toString();
    if (!addressToUse || !SOLANA_OFT_MINT_ADDRESS) {
      return null;
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mintPublicKey = new PublicKey(SOLANA_OFT_MINT_ADDRESS);
      const ownerPublicKey = new PublicKey(addressToUse);

      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPublicKey,
        ownerPublicKey
      );

      const tokenAccountInfo = await getAccount(connection, associatedTokenAddress);
      const balance = Number(tokenAccountInfo.amount) / Math.pow(10, SOLANA_TOKEN_DECIMALS);
      return balance.toFixed(6);
    } catch (error) {
      console.error("Error fetching Solana balance:", error);
      return "0";
    }
  }, [solanaWallet.publicKey]);

  const fetchEthereumBalance = useCallback(async (customAddress?: string) => {
    const addressToUse = customAddress || ethAddress;
    if (!addressToUse || !ETHEREUM_OFT_ADDRESS) {
      return null;
    }

    try {
      const response = await fetch(`https://api.etherscan.io/api?module=account&action=tokenbalance&contractaddress=${ETHEREUM_OFT_ADDRESS}&address=${addressToUse}&tag=latest&apikey=YourApiKeyToken`);
      const data = await response.json();

      if (data.status === '1') {
        const balance = Number(data.result) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS);
        return balance.toFixed(6);
      }
      return "0";
    } catch (error) {
      console.error("Error fetching Ethereum balance:", error);
      return "0";
    }
  }, [ethAddress]);

  const startDestinationBalancePolling = useCallback(async (destinationAddress: string, isEthereumDestination: boolean) => {
    setBridgeState(prev => ({ ...prev, isPollingDestination: true }));

    const initialBalance = isEthereumDestination
      ? await fetchEthereumBalance(destinationAddress)
      : await fetchSolanaBalance(destinationAddress);

    const pollInterval = setInterval(async () => {
      try {
        const currentBalance = isEthereumDestination
          ? await fetchEthereumBalance(destinationAddress)
          : await fetchSolanaBalance(destinationAddress);

        // Check if balance has increased (tokens arrived)
        if (currentBalance && initialBalance && parseFloat(currentBalance) > parseFloat(initialBalance)) {
          // Update the destination balance in state
          if (isEthereumDestination) {
            setBridgeState(prev => ({ ...prev, ethereumBalance: currentBalance, isPollingDestination: false, isBridging: false }));
          } else {
            setBridgeState(prev => ({ ...prev, solanaBalance: currentBalance, isPollingDestination: false, isBridging: false }));
          }
          clearInterval(pollInterval);
        }
      } catch (error) {
        console.error('Error polling destination balance:', error);
      }
    }, 5000); // Poll every 5 seconds

    // Stop polling after 10 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setBridgeState(prev => ({ ...prev, isPollingDestination: false, isBridging: false }));
    }, 600000);
  }, [fetchEthereumBalance, fetchSolanaBalance]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isClient) return;

    if (solanaWallet.connected && solanaWallet.publicKey) {
      fetchSolanaBalance().then(balance => {
        setBridgeState(prev => ({ ...prev, solanaBalance: balance }));
      });
    } else {
      setBridgeState(prev => ({ ...prev, solanaBalance: null }));
    }
  }, [isClient, solanaWallet.connected, solanaWallet.publicKey, fetchSolanaBalance]);

  useEffect(() => {
    if (!isClient) return;

    if (ethereumBalanceRaw && isEthConnected) {
      const ethBalance = (Number(ethereumBalanceRaw) / Math.pow(10, ETHEREUM_TOKEN_DECIMALS)).toFixed(6);
      setBridgeState(prev => ({ ...prev, ethereumBalance: ethBalance }));
    } else {
      setBridgeState(prev => ({ ...prev, ethereumBalance: null }));
    }
  }, [isClient, ethereumBalanceRaw, isEthConnected]);

  useEffect(() => {
    if (ethTxHash && isEthTxSuccess) {
      const layerZeroScanLink = getLayerZeroScanLink(ethTxHash, false);

      const newTransaction: Transaction = {
        hash: ethTxHash,
        timestamp: Date.now(),
        fromChain: 'ethereum',
        toChain: 'solana',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash: ethTxHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Immediately update origin balance (Ethereum balance will be updated by wagmi)
      // Start polling destination balance (Solana)
      const destinationAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey?.toString();
      if (destinationAddress) {
        startDestinationBalancePolling(destinationAddress, false);
      }
    }
  }, [ethTxHash, isEthTxSuccess, amount, bridgeState.customSolanaAddress, solanaWallet.publicKey, startDestinationBalancePolling]);

  useEffect(() => {
    if (isEthTxPending) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: true,
        error: null
      }));
    }
  }, [isEthTxPending]);

  useEffect(() => {
    if (ethTxError) {
      setBridgeState(prev => ({
        ...prev,
        isLoading: false,
        error: ethTxError.message || "Transaction failed"
      }));
    }
  }, [ethTxError]);

  // Add effect to reset button state when amount changes
  useEffect(() => {
    setBridgeState(prev => ({
      ...prev,
      nativeFee: null,
      txHash: null,
      layerZeroScanLink: null,
      error: null,
      receiveAmount: null,
    }));
  }, [amount, direction, bridgeState.customEthAddress, bridgeState.customSolanaAddress]);



  const resetBridgeState = useCallback(() => {
    setBridgeState(prev => ({
      ...prev,
      isLoading: false,
      error: null,
      txHash: null,
      nativeFee: null,
      receiveAmount: null,
      layerZeroScanLink: null,
      gasPrice: null,
    }));
  }, []);

  const validateInputs = useCallback(() => {
    if (!SOLANA_OFT_MINT_ADDRESS || !SOLANA_ESCROW_ADDRESS || !SOLANA_PROGRAM_ADDRESS || !ETHEREUM_OFT_ADDRESS) {
      throw new Error("Missing environment variables. Please check your .env.local file.");
    }

    if (direction === 'sol-to-eth') {
      if (!solanaWallet.connected || !solanaWallet.publicKey) {
        throw new Error("Please connect your Solana wallet first.");
      }
      if (!ethAddress && !bridgeState.customEthAddress) {
        throw new Error("Please connect your Ethereum wallet or enter a recipient address.");
      }
      if (bridgeState.customEthAddress && !isValidEthereumAddress(bridgeState.customEthAddress)) {
        throw new Error("Please enter a valid Ethereum address.");
      }
    } else {
      if (!isEthConnected || !ethAddress) {
        throw new Error("Please connect your Ethereum wallet first.");
      }
      if (!solanaWallet.publicKey && !bridgeState.customSolanaAddress) {
        throw new Error("Please connect your Solana wallet or enter a recipient address.");
      }
      if (bridgeState.customSolanaAddress && !isValidSolanaAddress(bridgeState.customSolanaAddress)) {
        throw new Error("Please enter a valid Solana address.");
      }
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      throw new Error("Please enter a valid amount.");
    }

    // Check if amount exceeds balance
    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      if (amountNum > solanaBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.solanaBalance || '0'} tokens.`);
      }
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      if (amountNum > ethereumBalance) {
        throw new Error(`Insufficient balance. You have ${bridgeState.ethereumBalance || '0'} tokens.`);
      }
    }
  }, [direction, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, amount, bridgeState.customEthAddress, bridgeState.customSolanaAddress, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const quoteSolanaToEthereum = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false,
        isQuoting: false
      }));
      return;
    }

    // Don't set isLoading for automatic quotes, only for manual ones
    if (!bridgeState.isQuoting) {
      setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));
    }

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));

      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      const { nativeFee } = await oft.quote(
        umi.rpc,
        {
          payer: publicKey(solanaWallet.publicKey!),
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
        },
        {
          payInLzToken: false,
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
        },
        [],
        (await getAddressLookupTable(connection, umi, SOLANA_MAINNET_EID)).lookupTableAddress
      );

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Solana has 6 decimals, Ethereum has 18 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        isLoading: false,
        isQuoting: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false,
        isQuoting: false
      }));
    }
  }, [validateInputs, amount, ethAddress, solanaWallet.publicKey, umi, bridgeState.customEthAddress, bridgeState.isQuoting]);

  const executeSolanaToEthereum = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      return; // Should not happen with automatic quoting
    }

    setBridgeState(prev => ({ ...prev, isBridging: true, isLoading: true, error: null }));

    try {
      const connection = new Connection(SOLANA_RPC_URL);
      const mint = publicKey(SOLANA_OFT_MINT_ADDRESS!);
      // Convert amount to proper decimals for Solana (6 decimals)
      const amountInTokens = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, SOLANA_TOKEN_DECIMALS)));
      // Use custom address if provided, otherwise use connected wallet address
      const recipientAddress = bridgeState.customEthAddress || ethAddress!;
      const recipientAddressBytes32 = addressToBytes32(recipientAddress);

      // Get token account
      const tokenAccount = findAssociatedTokenPda(umi, {
        mint: fromWeb3JsPublicKey(new PublicKey(SOLANA_OFT_MINT_ADDRESS!)),
        owner: publicKey(solanaWallet.publicKey!),
        tokenProgramId: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID),
      });

      const ix = await oft.send(
        umi.rpc,
        {
          payer: umi.identity,
          tokenMint: mint,
          tokenEscrow: publicKey(SOLANA_ESCROW_ADDRESS!),
          tokenSource: tokenAccount[0],
        },
        {
          to: Buffer.from(recipientAddressBytes32),
          dstEid: ETHEREUM_MAINNET_EID,
          amountLd: amountInTokens,
          minAmountLd: amountInTokens,
          options: Buffer.from(""),
          composeMsg: undefined,
          nativeFee: bridgeState.nativeFee,
        },
        {
          oft: publicKey(SOLANA_PROGRAM_ADDRESS!),
          token: fromWeb3JsPublicKey(TOKEN_PROGRAM_ID)
        }
      );

      // Build transaction with address lookup table and compute units
      let txB = transactionBuilder().add([ix]);
      txB = await addComputeUnitInstructions(
        connection,
        umi,
        SOLANA_MAINNET_EID,
        txB,
        umi.identity,
        4, // computeUnitPriceScaleFactor
        TransactionType.SendOFT
      );

      const {
        context: { slot: minContextSlot },
        value: { blockhash, lastValidBlockHeight }
      } = await connection.getLatestBlockhashAndContext('finalized');

      txB = txB.setBlockhash(blockhash);

      const umiTx = txB.build(umi);
      const web3Tx = toWeb3JsTransaction(umiTx);

      const signature = await solanaWallet.sendTransaction(web3Tx, connection, { minContextSlot });
      const txHash = signature;

      await connection.confirmTransaction({ blockhash, lastValidBlockHeight, signature });
      const layerZeroScanLink = getLayerZeroScanLink(txHash, false);

      // Add transaction to history
      const newTransaction: Transaction = {
        hash: txHash,
        timestamp: Date.now(),
        fromChain: 'solana',
        toChain: 'ethereum',
        amount: `${amount} tokens`,
        status: 'pending',
        layerZeroScanLink,
      };
      setTransactions(prev => [newTransaction, ...prev]);

      setBridgeState(prev => ({
        ...prev,
        txHash,
        layerZeroScanLink,
        isLoading: false
      }));

      // Immediately update origin balance after successful transaction
      if (solanaWallet.connected && solanaWallet.publicKey) {
        const newBalance = await fetchSolanaBalance();
        setBridgeState(prev => ({ ...prev, solanaBalance: newBalance }));
      }

      // Start polling destination balance
      const destinationAddress = bridgeState.customEthAddress || ethAddress!;
      await startDestinationBalancePolling(destinationAddress, true);
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false,
        isBridging: false
      }));
    }
  }, [bridgeState.nativeFee, amount, ethAddress, umi, fetchSolanaBalance, solanaWallet, bridgeState.customEthAddress, startDestinationBalancePolling]);

  const quoteEthereumToSolana = useCallback(async () => {
    try {
      validateInputs();
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Validation failed",
        isLoading: false,
        isQuoting: false
      }));
      return;
    }

    // Don't set isLoading for automatic quotes, only for manual ones
    if (!bridgeState.isQuoting) {
      setBridgeState(prev => ({ ...prev, isLoading: true, error: null }));
    }

    try {
      // Get current gas prices from Blocknative
      const gasPrice = await fetchGasPrice();

      // Calculate gas cost: gasLimit * maxFeePerGas
      let nativeFee: bigint;
      if (gasPrice) {
        nativeFee = ETHEREUM_GAS_LIMIT * gasPrice.maxFeePerGas;
      } else {
        // Fallback to a reasonable estimate if gas price fetch fails
        const fallbackGasPrice = BigInt(20 * 1e9); // 20 gwei
        nativeFee = ETHEREUM_GAS_LIMIT * fallbackGasPrice;
      }

      // Calculate receive amount (assuming 1:1 ratio, but with different decimals)
      // Ethereum has 18 decimals, Solana has 6 decimals
      const receiveAmount = parseFloat(amount).toFixed(6);

      setBridgeState(prev => ({
        ...prev,
        nativeFee,
        receiveAmount,
        gasPrice,
        isLoading: false,
        isQuoting: false
      }));
    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Failed to get quote",
        isLoading: false,
        isQuoting: false
      }));
    }
  }, [validateInputs, amount, bridgeState.isQuoting]);

  // Automatic quote fetching when inputs are valid
  useEffect(() => {
    const fetchQuoteAutomatically = async () => {
      // Don't fetch if already have a quote, currently loading, or have completed transaction
      if (bridgeState.nativeFee || bridgeState.isLoading || bridgeState.isQuoting || bridgeState.txHash) {
        return;
      }

      try {
        // Check if inputs are valid for quoting
        validateInputs();

        // Set quoting state
        setBridgeState(prev => ({ ...prev, isQuoting: true, error: null }));

        // Fetch quote based on direction
        if (direction === 'sol-to-eth') {
          await quoteSolanaToEthereum();
        } else {
          await quoteEthereumToSolana();
        }
      } catch {
        // Don't show validation errors for automatic quoting
        setBridgeState(prev => ({ ...prev, isQuoting: false }));
      }
    };

    // Debounce the quote fetching to avoid too many requests
    const timeoutId = setTimeout(fetchQuoteAutomatically, 500);
    return () => clearTimeout(timeoutId);
  }, [amount, direction, bridgeState.customEthAddress, bridgeState.customSolanaAddress, solanaWallet.connected, solanaWallet.publicKey, ethAddress, isEthConnected, bridgeState.nativeFee, bridgeState.isLoading, bridgeState.isQuoting, bridgeState.txHash, validateInputs, quoteSolanaToEthereum, quoteEthereumToSolana]);

  const executeEthereumToSolana = useCallback(async () => {
    if (!bridgeState.nativeFee) {
      return; // Should not happen with automatic quoting
    }

    setBridgeState(prev => ({ ...prev, isBridging: true, isLoading: true, error: null }));

    try {
      // Convert amount to proper decimals for Ethereum (18 decimals)
      const amountInTokens = parseUnits(amount, ETHEREUM_TOKEN_DECIMALS);

      // Use custom address if provided, otherwise use connected wallet address
      const recipientSolanaAddress = bridgeState.customSolanaAddress || solanaWallet.publicKey!.toString();
      const solanaAddressBytes = bs58.decode(recipientSolanaAddress);
      const recipientAddressBytes32 = `0x${Buffer.from(solanaAddressBytes).toString('hex').padStart(64, '0')}`;

      const sendParam = {
        dstEid: SOLANA_MAINNET_EID,
        to: recipientAddressBytes32 as `0x${string}`,
        amountLD: amountInTokens,
        minAmountLD: amountInTokens,
        extraOptions: '0x' as `0x${string}`,
        composeMsg: '0x' as `0x${string}`,
        oftCmd: '0x' as `0x${string}`,
      };

      const msgFee = {
        nativeFee: bridgeState.nativeFee,
        lzTokenFee: BigInt(0),
      };

      // Get current gas prices for the transaction
      const gasPrice = await fetchGasPrice();

      // Execute the actual contract transaction with optimized gas
      writeOftContract({
        address: ETHEREUM_OFT_ADDRESS as `0x${string}`,
        abi: oftAbi,
        functionName: 'send',
        args: [sendParam, msgFee, ethAddress as `0x${string}`],
        value: bridgeState.nativeFee,
        gas: ETHEREUM_GAS_LIMIT,
        ...(gasPrice && {
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas,
        }),
      });

      // The transaction hash will be available in ethTxHash after the transaction is submitted
      // We'll handle the success case in a useEffect that watches for ethTxHash changes

    } catch (error) {
      setBridgeState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Transaction failed",
        isLoading: false
      }));
    }
  }, [bridgeState.nativeFee, quoteEthereumToSolana, amount, solanaWallet.publicKey, ethAddress, writeOftContract, bridgeState.customSolanaAddress]);

  const hasInsufficientBalance = useMemo(() => {
    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) return false;

    if (direction === 'sol-to-eth') {
      const solanaBalance = parseFloat(bridgeState.solanaBalance || '0');
      return amountNum > solanaBalance;
    } else {
      const ethereumBalance = parseFloat(bridgeState.ethereumBalance || '0');
      return amountNum > ethereumBalance;
    }
  }, [direction, amount, bridgeState.solanaBalance, bridgeState.ethereumBalance]);

  const canQuote = useMemo(() => {
    const basicRequirements = direction === 'sol-to-eth'
      ? solanaWallet.connected && (ethAddress || bridgeState.customEthAddress)
      : isEthConnected && (solanaWallet.publicKey || bridgeState.customSolanaAddress);

    return basicRequirements && !hasInsufficientBalance;
  }, [direction, solanaWallet.connected, ethAddress, isEthConnected, solanaWallet.publicKey, bridgeState.customEthAddress, bridgeState.customSolanaAddress, hasInsufficientBalance]);

  const clearTransactionHistory = useCallback(() => {
    setTransactions([]);
  }, []);

  if (!isClient) return null;

  return (
    <div className="w-full">
      {/* Main Bridge Interface */}
      <div className="relative">
        {/* Bridge Interface */}
        <div className="bg-xavier-black border-4 border-xavier-white p-4 md:p-8 shadow-2xl flex flex-col md:flex-row gap-4 md:gap-6 max-w-5xl mx-auto">
          {/* From Token Section */}
          <div className="flex-1 min-w-0">
            <div className="bg-xavier-red border-2 border-xavier-white p-3 md:p-4">
              <div className="flex justify-between items-center mb-3">
                <span className="text-xavier-white font-impact text-lg tracking-wider">FROM</span>
                <span className="text-xavier-white font-impact text-sm tracking-wider">
                  BALANCE: {direction === 'sol-to-eth' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                </span>
              </div>
              <div className="flex items-center justify-between gap-3">
                <input
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.0"
                  className="bg-xavier-black text-xavier-white font-impact text-2xl outline-none border-2 border-xavier-white p-2 flex-1 min-w-0 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none tracking-wider"
                  step="0.000001"
                  min="0"
                />
                <div className="flex items-center space-x-2 bg-xavier-black border-2 border-xavier-white px-3 py-2 flex-shrink-0">
                  <img
                    src={direction === 'sol-to-eth' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                    alt={direction === 'sol-to-eth' ? 'Solana' : 'Ethereum'}
                    className="w-5 h-5"
                  />
                  <span className="text-xavier-white font-impact text-sm tracking-wider">XAVIER</span>
                </div>
              </div>
            </div>
          </div>

          {/* Swap Button */}
          <div className="flex items-center justify-center">
            <button
              onClick={() => {
                setDirection(direction === 'sol-to-eth' ? 'eth-to-sol' : 'sol-to-eth');
                resetBridgeState();
              }}
              className="bg-xavier-red hover:bg-red-700 border-4 border-xavier-white p-3 transition-colors"
            >
              <svg className="w-6 h-6 text-xavier-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={3}>
                <path strokeLinecap="round" strokeLinejoin="round" d="M8 7h12m0 0l-4-4m4 4l-4 4m-4 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </button>
          </div>

          {/* To Token Section */}
          <div className="flex-1 min-w-0">
            <div className="bg-xavier-red border-2 border-xavier-white p-3 md:p-4">
              <div className="flex justify-between items-center mb-3">
                <span className="text-xavier-white font-impact text-lg tracking-wider">TO</span>
                <div className="flex items-center gap-2">
                  {bridgeState.isPollingDestination && (
                    <svg className="animate-spin h-4 w-4 text-xavier-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  <span className="text-xavier-white font-impact text-sm tracking-wider">
                    BALANCE: {direction === 'eth-to-sol' ? bridgeState.solanaBalance || '0' : bridgeState.ethereumBalance || '0'}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between gap-3">
                <div className="bg-xavier-black text-xavier-white font-impact text-2xl border-2 border-xavier-white p-2 flex-1 min-w-0 overflow-hidden text-ellipsis tracking-wider">
                  {bridgeState.receiveAmount || '0.0'}
                </div>
                <div className="flex items-center space-x-2 bg-xavier-black border-2 border-xavier-white px-3 py-2 flex-shrink-0">
                  <img
                    src={direction === 'eth-to-sol' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                    alt={direction === 'eth-to-sol' ? 'Solana' : 'Ethereum'}
                    className="w-5 h-5"
                  />
                  <span className="text-xavier-white font-impact text-sm tracking-wider">XAVIER</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Wallet Connection and Action Section */}
        <div className="mt-4 md:mt-6 max-w-5xl mx-auto space-y-4 md:space-y-6">
          {/* Wallet Connection Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-3">
            {/* Solana Wallet */}
            <div className="flex items-center justify-between p-3 bg-xavier-black border-2 border-xavier-white">
              <div className="flex items-center space-x-3">
                <img src="/solana-sol-logo.svg" alt="Solana" className="w-6 h-6" />
                <span className="text-xavier-white font-impact tracking-wider">SOLANA</span>
              </div>
              {solanaWallet.connected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 font-impact text-sm tracking-wider">
                    {solanaWallet.publicKey?.toString().slice(0, 4)}...{solanaWallet.publicKey?.toString().slice(-4)}
                  </span>
                  <button
                    onClick={() => solanaWallet.disconnect()}
                    className="bg-xavier-red hover:bg-red-700 text-xavier-white font-impact text-sm px-3 py-1 border-2 border-xavier-white transition-colors tracking-wider"
                  >
                    DISCONNECT
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setSolanaWalletModalVisible(true)}
                  className="bg-xavier-red hover:bg-red-700 text-xavier-white font-impact text-sm py-2 px-4 border-2 border-xavier-white transition-colors tracking-wider"
                >
                  CONNECT WALLET
                </button>
              )}
            </div>

            {/* Ethereum Wallet */}
            <div className="flex items-center justify-between p-3 bg-xavier-black border-2 border-xavier-white">
              <div className="flex items-center space-x-3">
                <img src="/ethereum-eth-logo.svg" alt="Ethereum" className="w-6 h-6" />
                <span className="text-xavier-white font-impact tracking-wider">ETHEREUM</span>
              </div>
              {isEthConnected ? (
                <div className="flex items-center space-x-2">
                  <span className="text-green-400 font-impact text-sm tracking-wider">
                    {ethAddress?.slice(0, 6)}...{ethAddress?.slice(-4)}
                  </span>
                  <button
                    onClick={() => disconnect()}
                    className="bg-xavier-red hover:bg-red-700 text-xavier-white font-impact text-sm px-3 py-1 border-2 border-xavier-white transition-colors tracking-wider"
                  >
                    DISCONNECT
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setIsEthWalletModalOpen(true)}
                  className="bg-xavier-red hover:bg-red-700 text-xavier-white font-impact text-sm py-2 px-4 border-2 border-xavier-white transition-colors tracking-wider"
                >
                  CONNECT WALLET
                </button>
              )}
            </div>
          </div>

          {/* Custom Address Inputs */}
          {direction === 'sol-to-eth' && !ethAddress && (
            <div>
              <input
                type="text"
                value={bridgeState.customEthAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customEthAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-xavier-black border-2 border-xavier-white text-xavier-white placeholder-gray-400 focus:outline-none focus:border-xavier-red font-impact tracking-wider"
                placeholder="ENTER ETHEREUM ADDRESS (0X...)"
              />
            </div>
          )}

          {direction === 'eth-to-sol' && !solanaWallet.publicKey && (
            <div>
              <input
                type="text"
                value={bridgeState.customSolanaAddress}
                onChange={(e) => setBridgeState(prev => ({ ...prev, customSolanaAddress: e.target.value }))}
                className="w-full px-4 py-3 bg-xavier-black border-2 border-xavier-white text-xavier-white placeholder-gray-400 focus:outline-none focus:border-xavier-red font-impact tracking-wider"
                placeholder="ENTER SOLANA ADDRESS..."
              />
            </div>
          )}

          {/* Error Display */}
          {bridgeState.error && (
            <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md p-4 bg-xavier-red border-4 border-xavier-white">
              <p className="text-xavier-white font-impact tracking-wider">{bridgeState.error}</p>
            </div>
          )}

          {/* Action Button */}
          <div>
            {direction === 'sol-to-eth' ? (
              <button
                onClick={async () => {
                  await executeSolanaToEthereum();
                }}
                disabled={!canQuote || !bridgeState.nativeFee || bridgeState.isLoading || !!bridgeState.txHash}
                className="w-full py-4 px-6 bg-xavier-red hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-xavier-white font-impact text-xl tracking-wider transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center border-4 border-xavier-white min-h-[64px]"
              >
                {bridgeState.isLoading ? (
                  <>
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-xavier-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="font-impact tracking-wider">{bridgeState.nativeFee ? 'SENDING...' : 'GETTING QUOTE...'}</span>
                    </div>
                  </>
                ) : bridgeState.txHash ? (
                  <div className="flex items-center gap-3">
                    <span className="font-impact tracking-wider">TOKENS BRIDGED!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-yellow-300 hover:text-yellow-200 font-impact tracking-wider"
                        onClick={(e) => e.stopPropagation()}
                      >
                        VIEW TRANSACTION ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex items-center gap-3">
                    <span className="font-impact tracking-wider">BRIDGE</span>
                    <span className="text-sm text-gray-300 font-impact tracking-wider">
                      {(Number(bridgeState.nativeFee) / 1e9).toFixed(6)} SOL FEE
                    </span>
                  </div>
                ) : bridgeState.isQuoting ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-xavier-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="font-impact tracking-wider">GETTING QUOTE...</span>
                  </div>
                ) : (
                  <span className="font-impact tracking-wider">BRIDGE</span>
                )}
              </button>
            ) : (
              <button
                onClick={async () => {
                  await executeEthereumToSolana();
                }}
                disabled={!canQuote || !bridgeState.nativeFee || bridgeState.isLoading || !!bridgeState.txHash}
                className="w-full py-4 px-6 bg-xavier-red hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-xavier-white font-impact text-xl tracking-wider transition-all duration-200 transform hover:scale-[1.02] disabled:hover:scale-100 flex items-center justify-center border-4 border-xavier-white min-h-[64px]"
              >
                {bridgeState.isLoading ? (
                  <>
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-xavier-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="font-impact tracking-wider">{bridgeState.nativeFee ? 'SENDING...' : 'GETTING QUOTE...'}</span>
                    </div>
                  </>
                ) : bridgeState.txHash ? (
                  <div className="flex items-center gap-3">
                    <span className="font-impact tracking-wider">TOKENS BRIDGED!</span>
                    {bridgeState.layerZeroScanLink && (
                      <a
                        href={bridgeState.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-yellow-300 hover:text-yellow-200 font-impact tracking-wider"
                        onClick={(e) => e.stopPropagation()}
                      >
                        VIEW TRANSACTION ↗
                      </a>
                    )}
                  </div>
                ) : bridgeState.nativeFee ? (
                  <div className="flex items-center gap-3">
                    <span className="font-impact tracking-wider">BRIDGE</span>
                    <span className="text-sm text-gray-300 font-impact tracking-wider">
                      {(Number(bridgeState.nativeFee) / 1e18).toFixed(6)} ETH FEE
                    </span>
                  </div>
                ) : bridgeState.isQuoting ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-xavier-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span className="font-impact tracking-wider">GETTING QUOTE...</span>
                  </div>
                ) : (
                  <span className="font-impact tracking-wider">BRIDGE</span>
                )}
              </button>
            )}
          </div>

          {/* Transaction History Button & Panel */}
          {transactions.length > 0 && (
            <div className="mt-4 md:mt-8 text-center">
              <button
                onClick={() => setIsHistoryVisible(!isHistoryVisible)}
                className="text-xavier-white font-impact tracking-wider hover:text-gray-300 flex items-center gap-2 mx-auto"
              >
                <svg
                  className={`w-4 h-4 transition-transform ${isHistoryVisible ? 'transform rotate-180' : ''}`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  strokeWidth={3}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                </svg>
                {isHistoryVisible ? 'HIDE' : 'VIEW'} TRANSACTION HISTORY ({transactions.length})
              </button>

              {isHistoryVisible && (
                <div className="mt-3 md:mt-4 max-w-3xl mx-auto bg-xavier-black border-4 border-xavier-white p-3 md:p-4 text-left space-y-2 md:space-y-3">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-xavier-white font-impact tracking-wider text-lg">TRANSACTION HISTORY</h4>
                    <button
                      onClick={clearTransactionHistory}
                      className="text-xavier-red hover:text-red-400 font-impact tracking-wider"
                    >
                      CLEAR ALL
                    </button>
                  </div>

                  {transactions.map((tx, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-xavier-red border-2 border-xavier-white"
                    >
                      <div className="flex items-center gap-3">
                        <img
                          src={tx.fromChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.fromChain}
                          className="w-5 h-5"
                        />
                        <svg className="w-4 h-4 text-xavier-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={3}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                        <img
                          src={tx.toChain === 'solana' ? '/solana-sol-logo.svg' : '/ethereum-eth-logo.svg'}
                          alt={tx.toChain}
                          className="w-5 h-5"
                        />
                        <span className="text-xavier-white font-impact tracking-wider">{tx.amount}</span>
                      </div>
                      <a
                        href={tx.layerZeroScanLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-yellow-300 hover:text-yellow-200 font-impact tracking-wider"
                      >
                        <span>LAYERZERO SCAN</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={3}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </a>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Powered by LayerZero */}
          <div className="flex items-center justify-center pt-2 md:pt-4">
            <div className="flex items-center space-x-2 bg-xavier-black border-2 border-xavier-white px-4 py-2">
              <span className="text-xavier-white font-impact tracking-wider">POWERED BY</span>
              <img
                src="/layerzero.svg"
                alt="LayerZero"
                className="h-5 md:h-6 opacity-80"
              />
            </div>
          </div>
        </div>
      </div>

      {isEthWalletModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[100]">
          <div className="bg-xavier-black border-4 border-xavier-white p-6 max-w-sm w-full mx-4 relative">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xavier-white font-impact text-xl tracking-wider">CONNECT ETHEREUM WALLET</h3>
              <button
                onClick={() => setIsEthWalletModalOpen(false)}
                className="text-xavier-white hover:text-xavier-red"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={3}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-3">
              {connectors.map((connector) => (
                <button
                  key={connector.uid}
                  onClick={() => {
                    connect({ connector });
                    setIsEthWalletModalOpen(false);
                  }}
                  disabled={isConnectPending}
                  className="w-full flex items-center space-x-3 p-3 bg-xavier-red hover:bg-red-700 border-2 border-xavier-white transition-colors disabled:opacity-50"
                >
                  <div className="w-8 h-8 bg-xavier-black border-2 border-xavier-white flex items-center justify-center">
                    <span className="text-xavier-white font-impact text-sm">
                      {connector.name.charAt(0)}
                    </span>
                  </div>
                  <span className="text-xavier-white font-impact tracking-wider">{connector.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
