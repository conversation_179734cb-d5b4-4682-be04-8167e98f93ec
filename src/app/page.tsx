"use client";
import BridgeInterface from "@/components/BridgeInterface";
import Providers from "@/components/Providers";

export default function Home() {

  return (
    <Providers>
      <div className="min-h-screen bg-xavier-black flex flex-col">
        {/* Red Header */}
        <header className="bg-xavier-red border-b-4 border-xavier-black">
          <div className="container mx-auto px-4 py-6">
            <h1 className="text-xavier-white font-impact text-4xl md:text-6xl text-center tracking-wider">
              $XAVIER RENEGADE ANGEL BRIDGE
            </h1>
          </div>
        </header>

        {/* Banner Image */}
        <div className="w-full">
          <img
            src="/bannerxavier.jpg"
            alt="Xavier Banner"
            className="w-full h-32 md:h-48 object-cover object-center"
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8">
          <div className="w-full relative">
            <BridgeInterface />
          </div>
        </div>

        {/* Red Footer */}
        <footer className="bg-xavier-red border-t-4 border-xavier-black">
          <div className="container mx-auto px-4 py-6">
            <div className="text-center">
              <a
                href="https://xillions.org"
                target="_blank"
                rel="noopener noreferrer"
                className="text-xavier-white font-impact text-2xl md:text-4xl tracking-wider hover:text-gray-200 transition-colors"
              >
                XILLIONS.ORG MENTIONS THIS
              </a>
            </div>
          </div>
        </footer>
      </div>
    </Providers>
  );
}
